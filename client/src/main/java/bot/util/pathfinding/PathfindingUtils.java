package bot.util.pathfinding;

import bot.util.game.world.Tile;
import bot.util.game.entity.Player;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import rt4.CollisionMap;

import java.util.*;

/**
 * A* pathfinding utilities for bot navigation.
 * Provides methods for finding paths between tiles and optimizing paths for minimap walking.
 */
public class PathfindingUtils {
    private static final Logger logger = LoggerFactory.getLogger(PathfindingUtils.class);

    // Maximum search distance to prevent infinite loops
    private static final int MAX_SEARCH_DISTANCE = 100;
    
    // Collision flags
    private static final int BLOCKED = 0x200000; // Tile is completely blocked
    private static final int WALL_NORTH = 0x2;
    private static final int WALL_EAST = 0x8;
    private static final int WALL_SOUTH = 0x20;
    private static final int WALL_WEST = 0x80;

    /**
     * Finds a path from start to destination using A* pathfinding algorithm.
     * 
     * @param start The starting tile
     * @param destination The destination tile
     * @return List of tiles representing the path, or null if no path found
     */
    public static List<Tile> findPath(Tile start, Tile destination) {
        if (start == null || destination == null) {
            logger.warning("Start or destination is null");
            return null;
        }

        // Check if start and destination are too far apart
        double distance = start.distanceTo(destination);
        if (distance > MAX_SEARCH_DISTANCE) {
            logger.warning("Destination too far: " + distance + " tiles");
            return null;
        }

        logger.info("Finding path from " + start + " to " + destination + " (distance: " + String.format("%.1f", distance) + ")");

        // A* algorithm implementation
        PriorityQueue<AStarNode> openSet = new PriorityQueue<>(Comparator.comparingDouble(n -> n.fCost));
        Set<String> closedSet = new HashSet<>();
        Map<String, AStarNode> allNodes = new HashMap<>();

        // Create start node
        AStarNode startNode = new AStarNode(start, 0, start.distanceTo(destination), null);
        openSet.add(startNode);
        allNodes.put(tileKey(start), startNode);

        while (!openSet.isEmpty()) {
            AStarNode current = openSet.poll();
            String currentKey = tileKey(current.tile);

            // Check if we reached the destination
            if (current.tile.equals(destination) || current.tile.distanceTo(destination) <= 1.0) {
                logger.info("Path found! Reconstructing path...");
                return reconstructPath(current);
            }

            closedSet.add(currentKey);

            // Explore neighbors (8 directions)
            for (int dx = -1; dx <= 1; dx++) {
                for (int dy = -1; dy <= 1; dy++) {
                    if (dx == 0 && dy == 0) continue; // Skip current tile

                    Tile neighbor = new Tile(current.tile.getX() + dx, current.tile.getY() + dy, current.tile.getPlane());
                    String neighborKey = tileKey(neighbor);

                    // Skip if already processed
                    if (closedSet.contains(neighborKey)) continue;

                    // Check if movement to this neighbor is valid
                    if (!canMoveTo(current.tile, neighbor)) continue;

                    // Calculate costs
                    double moveCost = (dx != 0 && dy != 0) ? 1.414 : 1.0; // Diagonal movement costs more
                    double gCost = current.gCost + moveCost;
                    double hCost = neighbor.distanceTo(destination);

                    // Check if we already have a better path to this neighbor
                    AStarNode existingNode = allNodes.get(neighborKey);
                    if (existingNode != null && gCost >= existingNode.gCost) continue;

                    // Create or update neighbor node
                    AStarNode neighborNode = new AStarNode(neighbor, gCost, hCost, current);
                    allNodes.put(neighborKey, neighborNode);
                    openSet.add(neighborNode);
                }
            }
        }

        logger.warning("No path found to destination");
        return null;
    }

    /**
     * Optimizes a path by selecting waypoints at regular intervals.
     * This reduces the number of clicks needed for minimap walking.
     * 
     * @param path The original path
     * @param waypointDistance Distance between waypoints (in tiles)
     * @return Optimized list of waypoints
     */
    public static List<Tile> optimizePathToWaypoints(List<Tile> path, int waypointDistance) {
        if (path == null || path.isEmpty()) {
            return new ArrayList<>();
        }

        if (path.size() <= waypointDistance) {
            // Path is short enough, return as-is
            return new ArrayList<>(path);
        }

        List<Tile> waypoints = new ArrayList<>();
        
        // Always include the first tile
        waypoints.add(path.get(0));

        // Add waypoints at regular intervals
        for (int i = waypointDistance; i < path.size(); i += waypointDistance) {
            waypoints.add(path.get(i));
        }

        // Always include the last tile if it's not already included
        Tile lastTile = path.get(path.size() - 1);
        if (!waypoints.get(waypoints.size() - 1).equals(lastTile)) {
            waypoints.add(lastTile);
        }

        logger.info("Optimized path from " + path.size() + " tiles to " + waypoints.size() + " waypoints");
        return waypoints;
    }

    /**
     * Checks if movement from one tile to another is valid (no walls/obstacles blocking).
     * 
     * @param from Source tile
     * @param to Destination tile
     * @return true if movement is valid, false otherwise
     */
    private static boolean canMoveTo(Tile from, Tile to) {
        // Basic bounds checking
        if (to.getX() < 0 || to.getY() < 0 || to.getX() >= 104 || to.getY() >= 104) {
            return false;
        }

        try {
            // Get collision map for current plane
            CollisionMap collisionMap = rt4.collisionMaps[Player.plane];
            if (collisionMap == null) {
                // No collision data available, assume passable
                return true;
            }

            // Convert to local coordinates
            int fromLocalX = from.getX() - rt4.Camera.originX;
            int fromLocalY = from.getY() - rt4.Camera.originZ;
            int toLocalX = to.getX() - rt4.Camera.originX;
            int toLocalY = to.getY() - rt4.Camera.originZ;

            // Check bounds for local coordinates
            if (fromLocalX < 0 || fromLocalY < 0 || fromLocalX >= 104 || fromLocalY >= 104 ||
                toLocalX < 0 || toLocalY < 0 || toLocalX >= 104 || toLocalY >= 104) {
                return false;
            }

            // Check if destination tile is blocked
            int destFlags = collisionMap.flags[toLocalX][toLocalY];
            if ((destFlags & BLOCKED) != 0) {
                return false;
            }

            // Check for walls between tiles
            int dx = toLocalX - fromLocalX;
            int dy = toLocalY - fromLocalY;

            int sourceFlags = collisionMap.flags[fromLocalX][fromLocalY];

            // Check walls based on movement direction
            if (dx == 1 && dy == 0) { // Moving east
                return (sourceFlags & WALL_EAST) == 0;
            } else if (dx == -1 && dy == 0) { // Moving west
                return (sourceFlags & WALL_WEST) == 0;
            } else if (dx == 0 && dy == 1) { // Moving north
                return (sourceFlags & WALL_NORTH) == 0;
            } else if (dx == 0 && dy == -1) { // Moving south
                return (sourceFlags & WALL_SOUTH) == 0;
            } else if (dx != 0 && dy != 0) { // Diagonal movement
                // For diagonal movement, check both cardinal directions
                boolean canMoveX = (dx > 0) ? (sourceFlags & WALL_EAST) == 0 : (sourceFlags & WALL_WEST) == 0;
                boolean canMoveY = (dy > 0) ? (sourceFlags & WALL_NORTH) == 0 : (sourceFlags & WALL_SOUTH) == 0;
                return canMoveX && canMoveY;
            }

            return true;

        } catch (Exception e) {
            // If there's any error accessing collision data, assume passable
            logger.warning("Error checking collision for movement from " + from + " to " + to + ": " + e.getMessage());
            return true;
        }
    }

    /**
     * Reconstructs the path from the destination node back to the start.
     */
    private static List<Tile> reconstructPath(AStarNode destination) {
        List<Tile> path = new ArrayList<>();
        AStarNode current = destination;

        while (current != null) {
            path.add(current.tile);
            current = current.parent;
        }

        Collections.reverse(path);
        logger.info("Reconstructed path with " + path.size() + " tiles");
        return path;
    }

    /**
     * Creates a unique key for a tile for use in hash maps.
     */
    private static String tileKey(Tile tile) {
        return tile.getX() + "," + tile.getY() + "," + tile.getPlane();
    }

    /**
     * A* algorithm node class.
     */
    private static class AStarNode {
        final Tile tile;
        final double gCost; // Distance from start
        final double hCost; // Heuristic distance to destination
        final double fCost; // Total cost (g + h)
        final AStarNode parent;

        AStarNode(Tile tile, double gCost, double hCost, AStarNode parent) {
            this.tile = tile;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost;
            this.parent = parent;
        }
    }
}

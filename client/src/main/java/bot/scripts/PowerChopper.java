package bot.scripts;

import bot.api.interaction.InventoryInteraction;
import bot.api.builders.SceneryInteractionBuilder;
import bot.api.builders.WalkingBuilder;
import bot.util.game.AnimationUtils;
import bot.util.entity.EntityUtils;
import bot.util.game.world.Tile;
import bot.util.game.entity.Player;
import bot.api.script.BotScript;
import bot.api.script.ScriptCategory;
import bot.api.script.ScriptManifest;
import bot.util.error.TimeoutHandler;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import rt4.Scenery;

import java.util.concurrent.TimeoutException;

/**
 * A powerchopper script that chops trees based on woodcutting level:
 * - Level 1-14: Normal trees
 * - Level 15-29: Oak trees  
 * - Level 30+: Willow trees
 * Fills inventory then drops all logs. Automatically walks to trees when not on screen.
 */
@ScriptManifest(
        name = "Power Chopper",
        version = 1.0,
        description = "Chops trees based on level, fills inventory and drops logs",
        authors = {"Augment"},
        category = ScriptCategory.SKILLING,
        keywords = {"woodcutting", "chopping", "trees", "power", "dropping"}
)
public class PowerChopper implements BotScript {
    private static final Logger logger = LoggerFactory.getLogger(PowerChopper.class);

    // Script states
    private enum State {
        CHOPPING,
        DROPPING,
        WALKING_TO_TREE
    }

    // Skill ID for woodcutting
    private static final int WOODCUTTING_SKILL_ID = 8;

    // Tree object IDs
    private static final int NORMAL_TREE_ID = 1276;
    private static final int OAK_TREE_ID = 1277;
    private static final int WILLOW_TREE_ID = 1308;

    // Log item IDs
    private static final int NORMAL_LOGS_ID = 1511;
    private static final int OAK_LOGS_ID = 1521;
    private static final int WILLOW_LOGS_ID = 1519;

    // Axe item IDs for checking if player has an axe
    private static final int[] AXE_IDS = {1351, 1349, 1353, 1355, 1357, 1359}; // Bronze to Rune

    // Tree locations for different types (these are example locations - should be updated for specific areas)
    private static final Tile[] NORMAL_TREE_LOCATIONS = {
        new Tile(3164, 3288, 0), // Lumbridge
        new Tile(3165, 3289, 0),
        new Tile(3166, 3290, 0)
    };

    private static final Tile[] OAK_TREE_LOCATIONS = {
        new Tile(3166, 3419, 0), // Varrock West Bank area
        new Tile(3167, 3420, 0),
        new Tile(3168, 3421, 0)
    };

    private static final Tile[] WILLOW_TREE_LOCATIONS = {
        new Tile(3086, 3234, 0), // Draynor Village
        new Tile(3087, 3235, 0),
        new Tile(3088, 3236, 0)
    };

    // Current state and tree info
    private State currentState = State.CHOPPING;
    private int currentTreeId;
    private int currentLogId;
    private Tile[] currentTreeLocations;

    // Statistics
    private int logsChopped = 0;
    private int inventoriesDropped = 0;

    @Override
    public void onStart() {
        logger.info("Power Chopper started");

        // Check if player has an axe
        if (!hasAxe()) {
            logger.error("No axe found in inventory. Please equip an axe before starting.");
            bot.core.ScriptManager.getInstance().stopScript();
            return;
        }

        // Determine which tree to chop based on woodcutting level
        determineTreeType();
        
        logger.info("Woodcutting level: " + Player.getBaseLevel(WOODCUTTING_SKILL_ID));
        logger.info("Target tree type: " + getTreeName());

        // Determine initial state
        determineInitialState();
    }

    @Override
    public boolean onLoop() throws InterruptedException {
        // Update state if needed
        updateStateIfNeeded();

        switch (currentState) {
            case CHOPPING:
                return chopTree();

            case DROPPING:
                return dropLogs();

            case WALKING_TO_TREE:
                return walkToTree();

            default:
                logger.error("Unknown state: " + currentState);
                return false;
        }
    }

    @Override
    public void onStop() {
        logger.info("Power Chopper stopped");
        logger.info("Total logs chopped: " + logsChopped);
        logger.info("Total inventories dropped: " + inventoriesDropped);
    }

    /**
     * Determines which tree type to chop based on woodcutting level
     */
    private void determineTreeType() {
        int woodcuttingLevel = Player.getBaseLevel(WOODCUTTING_SKILL_ID);

        if (woodcuttingLevel >= 30) {
            // Willow trees
            currentTreeId = WILLOW_TREE_ID;
            currentLogId = WILLOW_LOGS_ID;
            currentTreeLocations = WILLOW_TREE_LOCATIONS;
        } else if (woodcuttingLevel >= 15) {
            // Oak trees
            currentTreeId = OAK_TREE_ID;
            currentLogId = OAK_LOGS_ID;
            currentTreeLocations = OAK_TREE_LOCATIONS;
        } else {
            // Normal trees
            currentTreeId = NORMAL_TREE_ID;
            currentLogId = NORMAL_LOGS_ID;
            currentTreeLocations = NORMAL_TREE_LOCATIONS;
        }
    }

    /**
     * Gets the name of the current tree type for logging
     */
    private String getTreeName() {
        if (currentTreeId == WILLOW_TREE_ID) return "Willow";
        if (currentTreeId == OAK_TREE_ID) return "Oak";
        return "Normal";
    }

    /**
     * Determines the initial state based on current conditions
     */
    private void determineInitialState() {
        if (InventoryInteraction.isInventoryFull()) {
            currentState = State.DROPPING;
            logger.info("Starting with full inventory. Dropping logs.");
        } else {
            currentState = State.CHOPPING;
            logger.info("Starting with space in inventory. Chopping trees.");
        }
    }

    /**
     * Updates the state based on current conditions
     */
    private void updateStateIfNeeded() {
        switch (currentState) {
            case CHOPPING:
                if (InventoryInteraction.isInventoryFull()) {
                    logger.info("Inventory full, switching to dropping");
                    currentState = State.DROPPING;
                }
                break;

            case DROPPING:
                if (!hasLogsInInventory()) {
                    logger.info("No more logs to drop, switching to chopping");
                    currentState = State.CHOPPING;
                }
                break;

            case WALKING_TO_TREE:
                // Check if we can see a tree now
                Scenery tree = EntityUtils.findNearestScenery(currentTreeId);
                if (tree != null) {
                    logger.info("Tree found, switching to chopping");
                    currentState = State.CHOPPING;
                }
                break;
        }
    }

    /**
     * Checks if the player has an axe in their inventory
     */
    private boolean hasAxe() {
        for (int axeId : AXE_IDS) {
            if (InventoryInteraction.inventoryContains(axeId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if the player has logs in their inventory
     */
    private boolean hasLogsInInventory() {
        return InventoryInteraction.inventoryContains(NORMAL_LOGS_ID) ||
               InventoryInteraction.inventoryContains(OAK_LOGS_ID) ||
               InventoryInteraction.inventoryContains(WILLOW_LOGS_ID);
    }

    /**
     * Chops a tree
     */
    private boolean chopTree() throws InterruptedException {
        // Find the nearest tree of the current type
        Scenery tree = EntityUtils.findNearestScenery(currentTreeId);

        if (tree == null) {
            logger.info("No " + getTreeName().toLowerCase() + " trees found nearby, walking to tree location");
            currentState = State.WALKING_TO_TREE;
            return true;
        }

        logger.info("Found " + getTreeName().toLowerCase() + " tree, chopping...");

        // Use SceneryInteractionBuilder to chop the tree
        boolean choppingStarted = false;
        try {
            choppingStarted = new SceneryInteractionBuilder(currentTreeId)
                .action("Chop down")
                .withDelay(300, 500)
                .execute();

            if (!choppingStarted) {
                logger.error("Failed to start chopping");
                return false;
            }
        } catch (TimeoutException e) {
            logger.error("Timeout while starting to chop: " + e.getMessage());
            return false;
        }

        // Wait for the animation to start
        if (AnimationUtils.getAnimation() == -1) {
            logger.info("Waiting for chopping animation to start");
            try {
                boolean animationStarted = AnimationUtils.waitForAnimationToStart(5000);
                if (!animationStarted) {
                    logger.error("Chopping animation didn't start");
                    return false;
                }
            } catch (TimeoutException e) {
                logger.error("Timeout waiting for chopping animation to start: " + e.getMessage());
                return false;
            }
        }

        // Wait for the animation to complete
        logger.info("Chopping in progress, waiting for completion");
        try {
            boolean animationCompleted = AnimationUtils.waitForAnimationToComplete(10000);
            if (animationCompleted) {
                logsChopped++;
                logger.info("Chopping completed. Total logs: " + logsChopped);
            }
        } catch (TimeoutException e) {
            logger.error("Timeout waiting for chopping animation to complete: " + e.getMessage());
        }

        // Add a small delay before next action
        Thread.sleep(random(500, 800));
        return true;
    }

    /**
     * Drops all logs from inventory
     */
    private boolean dropLogs() throws InterruptedException {
        logger.info("Dropping logs from inventory");

        // Drop normal logs
        if (InventoryInteraction.inventoryContains(NORMAL_LOGS_ID)) {
            boolean dropped = InventoryInteraction.interactWithInventoryItem(NORMAL_LOGS_ID, "Drop");
            if (dropped) {
                logger.info("Dropped normal logs");
                Thread.sleep(random(300, 500));
                return true;
            }
        }

        // Drop oak logs
        if (InventoryInteraction.inventoryContains(OAK_LOGS_ID)) {
            boolean dropped = InventoryInteraction.interactWithInventoryItem(OAK_LOGS_ID, "Drop");
            if (dropped) {
                logger.info("Dropped oak logs");
                Thread.sleep(random(300, 500));
                return true;
            }
        }

        // Drop willow logs
        if (InventoryInteraction.inventoryContains(WILLOW_LOGS_ID)) {
            boolean dropped = InventoryInteraction.interactWithInventoryItem(WILLOW_LOGS_ID, "Drop");
            if (dropped) {
                logger.info("Dropped willow logs");
                Thread.sleep(random(300, 500));
                return true;
            }
        }

        // If we get here, no more logs to drop
        if (!hasLogsInInventory()) {
            inventoriesDropped++;
            logger.info("All logs dropped. Total inventories dropped: " + inventoriesDropped);
            currentState = State.CHOPPING;
        }

        return true;
    }

    /**
     * Walks to the nearest tree location when trees are not visible
     */
    private boolean walkToTree() throws InterruptedException {
        logger.info("Walking to " + getTreeName().toLowerCase() + " tree location");

        // Find the closest tree location
        Tile playerLocation = Player.getLocation();
        Tile closestTreeLocation = null;
        double closestDistance = Double.MAX_VALUE;

        for (Tile treeLocation : currentTreeLocations) {
            double distance = playerLocation.distanceTo(treeLocation);
            if (distance < closestDistance) {
                closestDistance = distance;
                closestTreeLocation = treeLocation;
            }
        }

        if (closestTreeLocation == null) {
            logger.error("No tree locations defined for " + getTreeName().toLowerCase() + " trees");
            return false;
        }

        logger.info("Walking to closest " + getTreeName().toLowerCase() + " tree at " + closestTreeLocation);

        // Use WalkingBuilder to walk to the tree location
        try {
            boolean walkResult = new WalkingBuilder()
                    .to(closestTreeLocation)
                    .withDelay(500, 800)
                    .withTimeout(30000)
                    .execute();

            if (!walkResult) {
                logger.error("Failed to walk to tree location");
                return false;
            }

            logger.info("Reached tree location");
            currentState = State.CHOPPING;
            return true;

        } catch (TimeoutException e) {
            logger.error("Timeout while walking to tree: " + e.getMessage());
            return false;
        }
    }

    /**
     * Generates a random number between min and max (inclusive)
     */
    private int random(int min, int max) {
        return min + (int) (Math.random() * ((max - min) + 1));
    }
}

package bot.api.interaction;

import bot.util.game.AnimationUtils;
import bot.util.game.world.Tile;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.game.entity.Player;
import bot.util.error.TimeoutConfig;
import bot.util.error.TimeoutHandler;
import rt4.*;

import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Utility class for walking and movement in the game world
 * Provides methods for pathfinding, walking to locations, checking reachability,
 * and converting between coordinate systems. This class handles both local movement
 * (within the current scene) and global movement (across the entire game world).
 */
public class WalkInteraction {
    private static final Logger logger = LoggerFactory.getLogger("WalkInteraction");

    // Lock to prevent concurrent walking operations
    private static final ReentrantLock walkLock = new ReentrantLock();

    /**
     * Gets the player's current location
     *
     * @return The player's current location as a Tile
     */
    public static Tile getPlayerLocation() {
        return Player.getLocation();
    }



    /**
     * Checks if a tile is within interaction reach of the player
     * This method determines if the player can interact with objects or entities
     * at the specified tile without needing to move closer. The maximum interaction
     * distance is typically 1.5 tiles in Euclidean distance, and the tile must be
     * on the same plane as the player.
     *
     * @param tile The tile to check for reachability
     * @return true if the player can interact with the tile without moving, false otherwise
     */
    public static boolean isWithinReach(Tile tile) {


        // Get player's current location
        Tile playerLoc = getPlayerLocation();

        // Calculate distance to tile
        int deltaX = playerLoc.getX() - tile.getX();
        int deltaY = playerLoc.getY() - tile.getY();
        double distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // Check if the tile is on the same plane
        if (tile.getPlane() != playerLoc.getPlane()) {
            return false;
        }

        // Check if the tile is within interaction distance (typically 1-2 tiles)
        // We'll use a conservative value of 1.5 tiles
        if (distance > 1.5) {
            return false;
        }

        // Check if there's a direct path to the tile
        // This is a simplified check - in a full implementation, we would check for obstacles
        boolean pathExists = checkDirectPath(playerLoc, tile);
        if (!pathExists) {
            return false;
        }

        return true;
    }

    /**
     * Checks if there's a direct path between two tiles
     * This is a simplified implementation that doesn't check for all obstacles
     *
     * @param start The starting tile
     * @param end The ending tile
     * @return true if there's a direct path
     */
    private static boolean checkDirectPath(Tile start, Tile end) {
        // Get collision flags for the current plane
        int plane = start.getPlane();

        // Convert to local coordinates
        int startX = start.getX() - rt4.Camera.originX;
        int startY = start.getY() - rt4.Camera.originZ;
        int endX = end.getX() - rt4.Camera.originX;
        int endY = end.getY() - rt4.Camera.originZ;

        // Check if the tiles are within the scene bounds
        if (startX < 0 || startY < 0 || endX < 0 || endY < 0 ||
            startX >= 104 || startY >= 104 || endX >= 104 || endY >= 104) {
            return false;
        }

        // Check if the end tile is blocked
        int flags = SceneGraph.renderFlags[plane][endX][endY];
        if ((flags & 0x1280100) != 0) { // Check for wall and object blocking
            return false;
        }

        // For a more accurate check, we would trace the path between the tiles
        // and check for obstacles, but this simplified version just checks the end tile

        return true;
    }

    /**
     * Gets the player's current location in global coordinates
     * The game uses two coordinate systems: local (scene-relative) and global (world-relative).
     * This method automatically detects which coordinate system the player's location is in
     * and converts to global coordinates if necessary. Global coordinates are consistent
     * across the entire game world, while local coordinates change as the scene loads.
     *
     * @return The player's global location as a Tile (absolute world position)
     */
    public static Tile getPlayerGlobalLocation() {
        Tile playerLoc = getPlayerLocation();

        // Check if the player's coordinates are local
        boolean playerCoordsAreLocal = playerLoc.getX() < 1000 && playerLoc.getY() < 1000;

        if (playerCoordsAreLocal) {
            // Convert local coordinates to global
            return new Tile(
                playerLoc.getX() + rt4.Camera.originX,
                playerLoc.getY() + rt4.Camera.originZ,
                playerLoc.getPlane()
            );
        } else {
            // Already in global coordinates
            return playerLoc;
        }
    }

    /**
     * Checks if the player is moving
     *
     * @return true if the player is moving
     */
    public static boolean isMoving() {
        return AnimationUtils.isMoving();
    }



    /**
     * Clicks on the minimap to walk to a specific tile
     * This method handles the conversion between global and local coordinates,
     * calculates the correct position on the minimap, and simulates a mouse click
     * to initiate walking. It will wait for the player to start moving and can
     * handle interrupted threads.
     *
     * @param tile The destination tile to walk to (in global coordinates)
     * @return true if the walking action was successfully initiated, false otherwise
     * @throws InterruptedException if the thread is interrupted while waiting
     */
    public static boolean walkToTile(Tile tile) throws InterruptedException {


        // Check if thread has been interrupted
        if (Thread.currentThread().isInterrupted()) {
            throw new InterruptedException("Thread interrupted before walking to tile");
        }

        // Check if the tile is on the same plane
        if (tile.getPlane() != rt4.Player.plane) {
            return false;
        }

        // Get player's current location in tile coordinates
        Tile playerLoc = getPlayerLocation();

        // Check if player coordinates are local (scene-relative) or global
        boolean playerCoordsAreLocal = playerLoc.getX() < 1000 && playerLoc.getY() < 1000;
        int adjustedTargetX = tile.getX();
        int adjustedTargetY = tile.getY();

        if (playerCoordsAreLocal) {
            // Convert global target coordinates to local coordinates
            adjustedTargetX = tile.getX() - rt4.Camera.originX;
            adjustedTargetY = tile.getY() - rt4.Camera.originZ;
        } else {

        }

        // Calculate the difference and distance
        int diffX = adjustedTargetX - playerLoc.getX();
        int diffY = adjustedTargetY - playerLoc.getY();
        double distance = Math.sqrt(diffX * diffX + diffY * diffY);


        // If we're already at the target tile, return success
        if (distance < 1) {
            return true;
        }

        // Get the target tile coordinates
        int targetX = tile.getX();
        int targetY = tile.getY();

        // Convert to local coordinates for pathfinding
        int localX = playerCoordsAreLocal ? adjustedTargetX : targetX - rt4.Camera.originX;
        int localY = playerCoordsAreLocal ? adjustedTargetY : targetY - rt4.Camera.originZ;


        // Calculate the difference and distance to target
        diffX = playerCoordsAreLocal ? (localX - playerLoc.getX()) : (targetX - playerLoc.getX());
        diffY = playerCoordsAreLocal ? (localY - playerLoc.getY()) : (targetY - playerLoc.getY());
        distance = Math.sqrt(diffX * diffX + diffY * diffY);


        // Minimap constants
        int minimapCenterX = 630;
        int minimapCenterY = 85;
        float minimapRadius = 76.5f;

        // Initialize minimap coordinates to center
        int minimapX = minimapCenterX;
        int minimapY = minimapCenterY;

        // Calculate minimap click position if not already at destination
        if (distance > 1) {
            // Calculate flag screen position using game's formula
            int flagScreenX = localX * 4 + 2 - rt4.PlayerList.self.xFine / 32;
            int flagScreenY = localY * 4 + 2 - rt4.PlayerList.self.zFine / 32;

            // Get camera rotation values
            int cameraYaw = (rt4.MiniMap.anInt1814 + (int) rt4.Camera.yawTarget) & 0x7FF;
            int zoomFactor = rt4.MiniMap.anInt4130 + 256;

            // Apply rotation and scaling
            int sinYaw = rt4.MathUtils.sin[cameraYaw];
            int cosYaw = rt4.MathUtils.cos[cameraYaw];
            int scaledSin = sinYaw * 256 / zoomFactor;
            int scaledCos = cosYaw * 256 / zoomFactor;
            int rotatedX = (flagScreenX * scaledCos + flagScreenY * scaledSin) >> 16;
            int rotatedY = (flagScreenY * scaledCos - flagScreenX * scaledSin) >> 16;

            // Calculate final screen coordinates
            minimapX = minimapCenterX + rotatedX;
            minimapY = minimapCenterY - rotatedY; // Y is inverted


        }

        // Ensure coordinates are within minimap bounds
        minimapX = Math.max(minimapCenterX - (int)minimapRadius, Math.min(minimapX, minimapCenterX + (int)minimapRadius));
        minimapY = Math.max(minimapCenterY - (int)minimapRadius, Math.min(minimapY, minimapCenterY + (int)minimapRadius));


        // Move mouse to calculated position using MouseInteractionUtil
        bot.impl.input.MouseHandler.moveMouse(minimapX, minimapY);
        long startTime = System.currentTimeMillis();

        try {
            // Check if thread has been interrupted
            if (Thread.currentThread().isInterrupted()) {

                throw new InterruptedException("Thread interrupted before mouse click");
            }

            // Store the current mouse position
            int currentX = Mouse.currentMouseX;
            int currentY = Mouse.currentMouseY;

            // Simulate mouse click
            MenuInteraction.leftClick();

            // Ensure Cross is set to the current mouse position
            Cross.x = currentX;
            Cross.y = currentY;
            Cross.type = 2;
            Cross.milliseconds = 0;

            // Wait for click to be processed
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {

                throw e;
            }

            // Find path to destination using game's PathFinder
            boolean pathFound = rt4.PathFinder.findPath(
                rt4.PlayerList.self.movementQueueZ[0], // Start Z
                0, // Type 0
                0, // Size 0
                true, // Run
                0, // Unused
                localX, // Destination X (local)
                0, // Unused
                0, // Unused
                1, // Mode 1 (minimap walking)
                localY, // Destination Y (local)
                rt4.PlayerList.self.movementQueueX[0] // Start X
            );



            // Check if thread has been interrupted
            if (Thread.currentThread().isInterrupted()) {

                throw new InterruptedException("Thread interrupted before sending walk packet");
            }

            if (pathFound) {
                // Set destination flag right before sending packet
                rt4.LoginManager.mapFlagX = localX;
                rt4.LoginManager.mapFlagZ = localY;

                // Send walk packet with all required parameters
                Protocol.outboundBuffer.p1(InterfaceList.anInt5);
                Protocol.outboundBuffer.p1(MiniMenu.anInt2878);
                Protocol.outboundBuffer.p2((int) rt4.Camera.yawTarget);
                Protocol.outboundBuffer.p1(57);
                Protocol.outboundBuffer.p1(MiniMap.anInt1814);
                Protocol.outboundBuffer.p1(MiniMap.anInt4130);
                Protocol.outboundBuffer.p1(89);
                Protocol.outboundBuffer.p2(PlayerList.self.xFine);
                Protocol.outboundBuffer.p2(PlayerList.self.zFine);
                Protocol.outboundBuffer.p1(PathFinder.anInt4364);
                Protocol.outboundBuffer.p1(63);

                // Send the packet
                rt4.Protocol.socket.write(rt4.Protocol.outboundBuffer.data, rt4.Protocol.outboundBuffer.offset);
                return true;
            } else {
                return false;
            }
        } catch (InterruptedException e) {

            throw e; // Re-throw the InterruptedException to properly stop the script
        } catch (Exception e) {

            return false;
        }
    }

    /**
     * Walks a predefined path of tiles
     * This method intelligently navigates a series of waypoints by finding the closest
     * point in the path to start from, then walking from waypoint to waypoint. It handles
     * timeout conditions, thread interruptions, and automatically adjusts if the player
     * is already close to a waypoint.
     *
     * @param path An array of Tiles defining the path to walk (in global coordinates)
     * @return true if the entire path was successfully walked, false if there was an error
     * @throws InterruptedException if the thread is interrupted during walking
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if path is null or empty
     */
    public static boolean walkPath(Tile[] path) throws InterruptedException, TimeoutException {
        return walkPath(path, TimeoutHandler.getDefaultConfig());
    }

    /**
     * Walks a predefined path of tiles using a specific TimeoutConfig
     * This method intelligently navigates a series of waypoints by finding the closest
     * point in the path to start from, then walking from waypoint to waypoint. It handles
     * timeout conditions, thread interruptions, and automatically adjusts if the player
     * is already close to a waypoint.
     *
     * @param path An array of Tiles defining the path to walk (in global coordinates)
     * @param config The TimeoutConfig to use for timeout values
     * @return true if the entire path was successfully walked, false if there was an error
     * @throws InterruptedException if the thread is interrupted during walking
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if path is null or empty, or config is null
     */
    public static boolean walkPath(Tile[] path, TimeoutConfig config) throws InterruptedException, TimeoutException {
        if (config == null) {
            throw new IllegalArgumentException("TimeoutConfig cannot be null");
        }
        return walkPath(path, config.getWalkTimeout());
    }

    /**
     * Walks a predefined path of tiles
     * This method intelligently navigates a series of waypoints by finding the closest
     * point in the path to start from, then walking from waypoint to waypoint. It handles
     * timeout conditions, thread interruptions, and automatically adjusts if the player
     * is already close to a waypoint.
     *
     * @param path An array of Tiles defining the path to walk (in global coordinates)
     * @param timeout The maximum time to wait for reaching each waypoint (in milliseconds)
     * @return true if the entire path was successfully walked, false if there was an error
     * @throws InterruptedException if the thread is interrupted during walking
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if path is null or empty, or timeout is negative
     */
    public static boolean walkPath(Tile[] path, long timeout) throws InterruptedException, TimeoutException {


        // Validate input
        if (path == null || path.length == 0) {
            throw new IllegalArgumentException("Cannot walk an empty path");
        }
        if (timeout < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeout);
        }

        // Acquire the walk lock to prevent concurrent walking operations
        if (!walkLock.tryLock()) {
            logger.warning("Another walking operation is in progress, waiting for lock...");
            walkLock.lock();
        }

        try {

        // Check if thread has been interrupted
        if (Thread.currentThread().isInterrupted()) {

            throw new InterruptedException("Thread interrupted before starting path");
        }

        // Find the closest tile in the path to start from
        // Make sure we're using global coordinates for comparison
        Tile playerLoc = getPlayerGlobalLocation();
        int closestIndex = 0;
        double closestDistance = Double.MAX_VALUE;

        // Log player location for debugging
        logger.info("Player global location: " + playerLoc);

        // Ensure all path tiles are in global coordinates
        Tile[] globalPath = new Tile[path.length];
        for (int i = 0; i < path.length; i++) {
            // Check if the path tile is in local coordinates
            if (path[i].getX() < 1000 && path[i].getY() < 1000) {
                // Convert to global coordinates
                globalPath[i] = path[i].asGlobal();
            } else {
                // Already in global coordinates
                globalPath[i] = path[i];
            }
            // Log path tile for debugging
            logger.info("Path tile " + i + ": " + globalPath[i]);
        }

        // Find the closest tile in the global path
        for (int i = 0; i < globalPath.length; i++) {
            double distance = playerLoc.distanceTo(globalPath[i]);
            logger.info("Distance to path tile " + i + ": " + distance);

            if (distance < closestDistance) {
                closestDistance = distance;
                closestIndex = i;
            }
        }

        // Start from the closest waypoint
        int currentIndex = closestIndex;



        // If we're very close to the closest waypoint, move to the next one
        if (closestDistance < 3 && currentIndex < path.length - 1) {
            currentIndex++;

        }



        // Loop through the path starting from the current index
        for (int i = currentIndex; i < path.length; i++) {
            // Check if the script is paused

            // Check if thread has been interrupted
            if (Thread.currentThread().isInterrupted()) {
                throw new InterruptedException("Thread interrupted during path walking");
            }

            // Get the current waypoint
            Tile currentWaypoint = path[i];

            // Get player location in global coordinates
            playerLoc = getPlayerGlobalLocation();

            // Ensure waypoint is in global coordinates
            Tile globalWaypoint = currentWaypoint;
            if (currentWaypoint.getX() < 1000 && currentWaypoint.getY() < 1000) {
                globalWaypoint = currentWaypoint.asGlobal();
            }

            // Calculate distance to waypoint using the Tile.distanceTo method
            double distance = playerLoc.distanceTo(globalWaypoint);
            logger.info("Distance to current waypoint: " + distance);

            // Only walk to tiles that are within reasonable distance
            if (distance < 20) {

                // Walk to the current waypoint
                boolean walkResult = walkToTile(currentWaypoint);

                if (!walkResult) {
                    return false;
                }

                // Wait until we're close to the destination or movement stops
                boolean reachedWaypoint;
                try {
                    reachedWaypoint = waitUntilCloseToDestination(currentWaypoint, 4, timeout);
                } catch (TimeoutException e) {
                    logger.warning("Timeout while waiting for waypoint: " + e.getMessage());
                    throw e; // Propagate the timeout exception
                }

                if (!reachedWaypoint) {
                    // Check if the script is paused

                    // Check if we were interrupted
                    if (Thread.currentThread().isInterrupted()) {
                        throw new InterruptedException("Thread interrupted while waiting for waypoint");
                    }

                    // Continue anyway, as we might be close enough for the next waypoint
                }

                // Check if we've reached the end of the path
                if (i == path.length - 1) {
                    return true;
                }
            }
        }

        // If we've gone through all waypoints, we've completed the path
        return true;
        } finally {
            // Always release the lock
            walkLock.unlock();
        }
    }



    /**
     * Reverses an array of Tiles to create a return path
     * This utility method takes a path and creates a new array with the tiles in reverse order,
     * which is useful for creating return journeys. For example, if you have a path from
     * a bank to a mining site, you can reverse it to get a path from the mining site back
     * to the bank.
     *
     * @param path The original path to reverse
     * @return A new array with the tiles in reverse order, or the original array if it's null or has 0-1 elements
     */
    public static Tile[] reversePath(Tile[] path) {
        if (path == null || path.length <= 1) {
            return path; // Nothing to reverse
        }

        Tile[] reversedPath = new Tile[path.length];
        for (int i = 0; i < path.length; i++) {
            reversedPath[i] = path[path.length - 1 - i];
        }

        return reversedPath;
    }

    /**
     * Walks to a nearby destination using the minimap
     * This method is optimized for short-distance walking within the current scene.
     * It attempts to walk directly to the destination and then waits until the player
     * either reaches the destination or the timeout expires. This is particularly useful
     * for walking to nearby entities like NPCs, objects, or ground items that are not
     * currently visible on screen.
     *
     * @param destination The destination tile to walk to (in global coordinates)
     * @return true if the destination was successfully reached, false otherwise
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null
     */
    public static boolean walkToLocal(Tile destination) throws InterruptedException, TimeoutException {
        return walkToLocal(destination, TimeoutHandler.getDefaultConfig());
    }

    /**
     * Walks to a nearby destination using the minimap with a specific TimeoutConfig
     * This method is optimized for short-distance walking within the current scene.
     * It attempts to walk directly to the destination and then waits until the player
     * either reaches the destination or the timeout expires. This is particularly useful
     * for walking to nearby entities like NPCs, objects, or ground items that are not
     * currently visible on screen.
     *
     * @param destination The destination tile to walk to (in global coordinates)
     * @param config The TimeoutConfig to use for timeout values
     * @return true if the destination was successfully reached, false otherwise
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null or config is null
     */
    public static boolean walkToLocal(Tile destination, TimeoutConfig config) throws InterruptedException, TimeoutException {
        if (config == null) {
            throw new IllegalArgumentException("TimeoutConfig cannot be null");
        }
        return walkToLocal(destination, config.getWalkTimeout());
    }

    /**
     * Walks to a nearby destination using the minimap
     * This method is optimized for short-distance walking within the current scene.
     * It attempts to walk directly to the destination and then waits until the player
     * either reaches the destination or the timeout expires. This is particularly useful
     * for walking to nearby entities like NPCs, objects, or ground items that are not
     * currently visible on screen.
     *
     * @param destination The destination tile to walk to (in global coordinates)
     * @param timeout The maximum time to wait for reaching the destination (in milliseconds)
     * @return true if the destination was successfully reached, false otherwise
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null or timeout is negative
     */
    public static boolean walkToLocal(Tile destination, long timeout) throws InterruptedException, TimeoutException {
        // Validate parameters
        if (destination == null) {
            throw new IllegalArgumentException("Destination cannot be null");
        }
        if (timeout < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeout);
        }

        // Acquire the walk lock to prevent concurrent walking operations
        if (!walkLock.tryLock()) {
            logger.warning("Another walking operation is in progress, waiting for lock...");
            walkLock.lock();
        }

        try {
            // First, try to walk directly to the destination
            boolean walkResult = walkToTile(destination);

            if (!walkResult) {
                return false;
            }

            // Wait until we're close to the destination or timeout
            try {
                return waitUntilCloseToDestination(destination, 4, timeout);
            } catch (TimeoutException e) {
                logger.warning("Timeout while waiting to reach destination: " + e.getMessage());
                throw e; // Propagate the timeout exception to be handled by the caller
            }
        } finally {
            // Always release the lock
            walkLock.unlock();
        }
    }





    /**
     * Waits until the player is within a certain distance of the destination or until timeout
     * This method continuously checks the player's position and compares it to the destination
     * tile. It returns true when the player is within the specified maximum distance of the
     * destination, or false if the timeout expires before reaching the destination. The method
     * includes an initial delay to allow the player to start moving and handles thread
     * interruptions properly.
     *
     * @param destination The destination tile to check distance to (in global coordinates)
     * @param maxDistance The maximum distance in tiles to consider "close enough" to the destination
     * @return true if the player reached the destination within the timeout, false if the timeout expired
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null or maxDistance is negative
     */
    public static boolean waitUntilCloseToDestination(Tile destination, double maxDistance) throws InterruptedException, TimeoutException {
        return waitUntilCloseToDestination(destination, maxDistance, TimeoutHandler.getDefaultConfig());
    }

    /**
     * Waits until the player is within a certain distance of the destination or until timeout
     * This method continuously checks the player's position and compares it to the destination
     * tile. It returns true when the player is within the specified maximum distance of the
     * destination, or false if the timeout expires before reaching the destination. The method
     * includes an initial delay to allow the player to start moving and handles thread
     * interruptions properly.
     *
     * @param destination The destination tile to check distance to (in global coordinates)
     * @param maxDistance The maximum distance in tiles to consider "close enough" to the destination
     * @param config The TimeoutConfig to use for timeout values
     * @return true if the player reached the destination within the timeout, false if the timeout expired
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null, maxDistance is negative, or config is null
     */
    public static boolean waitUntilCloseToDestination(Tile destination, double maxDistance, TimeoutConfig config) throws InterruptedException, TimeoutException {
        if (config == null) {
            throw new IllegalArgumentException("TimeoutConfig cannot be null");
        }
        return waitUntilCloseToDestination(destination, maxDistance, config.getWalkTimeout());
    }

    /**
     * Waits until the player is within a certain distance of the destination or until timeout
     * This method continuously checks the player's position and compares it to the destination
     * tile. It returns true when the player is within the specified maximum distance of the
     * destination, or false if the timeout expires before reaching the destination. The method
     * includes an initial delay to allow the player to start moving and handles thread
     * interruptions properly.
     *
     * @param destination The destination tile to check distance to (in global coordinates)
     * @param maxDistance The maximum distance in tiles to consider "close enough" to the destination
     * @param timeout The maximum time to wait in milliseconds before giving up
     * @return true if the player reached the destination within the timeout, false if the timeout expired
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null, maxDistance is negative, or timeout is negative
     */
    public static boolean waitUntilCloseToDestination(Tile destination, double maxDistance, long timeout) throws InterruptedException, TimeoutException {
        // Validate parameters
        if (destination == null) {
            throw new IllegalArgumentException("Destination cannot be null");
        }
        if (maxDistance < 0) {
            throw new IllegalArgumentException("Max distance cannot be negative: " + maxDistance);
        }
        if (timeout < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeout);
        }

        // Check if the script is paused
        long startTime = System.currentTimeMillis();
        long endTime = startTime + timeout;

        // Check if thread has been interrupted before starting
        if (Thread.currentThread().isInterrupted()) {
            throw new InterruptedException("Thread interrupted before waiting for destination");
        }

        // Initial sleep to allow the player to start moving
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw e; // Re-throw to properly stop the script
        }

        // Wait until we're close enough to the destination or timeout
        while (System.currentTimeMillis() < endTime) {
            // Check if the script is paused

            // Check if thread has been interrupted (check frequently)
            if (Thread.currentThread().isInterrupted()) {
                throw new InterruptedException("Thread interrupted while waiting for destination");
            }

            // Get player location in global coordinates
            Tile playerLoc = getPlayerGlobalLocation();

            // Ensure destination is in global coordinates
            Tile globalDestination = destination;
            if (destination.getX() < 1000 && destination.getY() < 1000) {
                globalDestination = destination.asGlobal();
            }

            // Calculate distance to destination using the Tile.distanceTo method
            double distance = playerLoc.distanceTo(globalDestination);
            logger.info("Current distance to destination: " + String.format("%.1f", distance) + " tiles");

            // If we're close enough to the destination, return success
            if (distance <= maxDistance) {
                logger.info("Successfully reached destination (distance: " + String.format("%.1f", distance) + " tiles)");
                return true;
            }

            // If the player has stopped moving and we're not at the destination yet,
            // we might be stuck or have reached as close as we can get
            if (!isMoving()) {
                // Check for interruption again
                if (Thread.currentThread().isInterrupted()) {
                    throw new InterruptedException("Thread interrupted while checking if movement stopped");
                }

                // Wait a bit to confirm we've actually stopped
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    throw e; // Re-throw to properly stop the script
                }

                // Check for interruption again
                if (Thread.currentThread().isInterrupted()) {
                    throw new InterruptedException("Thread interrupted after confirming movement stopped");
                }

                // Check again if we're still not moving
                if (!isMoving()) {
                    return false;
                }
            }

            // Sleep for a short time before checking again
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                throw e; // Re-throw to properly stop the script
            }
        }

        // If we've reached here, we've timed out
        throw new TimeoutException("Timeout waiting to reach destination: " + destination + ", timeout: " + timeout + "ms");
    }


}

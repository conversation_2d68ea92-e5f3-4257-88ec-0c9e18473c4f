package bot.api.interaction;

import bot.util.game.world.Tile;
import bot.util.game.entity.Player;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.pathfinding.PathfindingUtils;
import bot.util.error.TimeoutHandler;

import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.ReentrantLock;
import java.util.List;

/**
 * Clean, simplified walking utility class.
 * Uses custom A* pathfinding exclusively since the game's PathFinder is broken.
 * Provides straightforward methods for walking to tiles and following paths.
 */
public class WalkInteractionClean {
    private static final Logger logger = LoggerFactory.getLogger(WalkInteractionClean.class);
    
    // Lock to prevent concurrent walking operations
    private static final ReentrantLock walkLock = new ReentrantLock();
    
    // Default timeout values
    private static final long DEFAULT_WALK_TIMEOUT = 30000; // 30 seconds
    private static final long DEFAULT_WAYPOINT_TIMEOUT = 10000; // 10 seconds per waypoint
    
    // Distance thresholds
    private static final double WAYPOINT_REACHED_DISTANCE = 2.0; // Distance to consider waypoint reached

    /**
     * Main walking method - walks to any tile using custom pathfinding.
     * This is the primary method that all other walking methods should use.
     * 
     * @param destination The tile to walk to (global coordinates)
     * @param timeout Maximum time to wait for walking to complete (milliseconds)
     * @return true if successfully reached destination, false otherwise
     * @throws InterruptedException if thread is interrupted
     * @throws TimeoutException if walking times out
     */
    public static boolean walkTo(Tile destination, long timeout) throws InterruptedException, TimeoutException {
        if (destination == null) {
            throw new IllegalArgumentException("Destination cannot be null");
        }
        if (timeout <= 0) {
            timeout = DEFAULT_WALK_TIMEOUT;
        }

        // Acquire lock to prevent concurrent walking
        if (!walkLock.tryLock()) {
            logger.warning("Another walking operation in progress, waiting...");
            walkLock.lock();
        }

        try {
            Tile playerLoc = Player.getLocation();
            logger.info("Walking from " + playerLoc + " to " + destination);

            // Check if already at destination
            if (playerLoc.distanceTo(destination) <= WAYPOINT_REACHED_DISTANCE) {
                logger.info("Already at destination");
                return true;
            }

            // Use custom A* pathfinding to generate path
            List<Tile> path = PathfindingUtils.findPath(playerLoc, destination);
            if (path == null || path.isEmpty()) {
                logger.warning("Could not find path to destination");
                return false;
            }

            logger.info("Generated path with " + path.size() + " tiles");

            // Convert path to waypoints (every 5 tiles for minimap clicking)
            List<Tile> waypoints = PathfindingUtils.optimizePathToWaypoints(path, 5);
            logger.info("Optimized to " + waypoints.size() + " waypoints");

            // Walk each waypoint
            for (int i = 0; i < waypoints.size(); i++) {
                if (Thread.currentThread().isInterrupted()) {
                    throw new InterruptedException("Walking interrupted");
                }

                Tile waypoint = waypoints.get(i);
                logger.info("Walking to waypoint " + (i + 1) + "/" + waypoints.size() + ": " + waypoint);

                if (!walkToWaypoint(waypoint, DEFAULT_WAYPOINT_TIMEOUT)) {
                    logger.warning("Failed to reach waypoint " + waypoint);
                    return false;
                }
            }

            // Final check - are we at the destination?
            Tile finalLoc = Player.getLocation();
            double finalDistance = finalLoc.distanceTo(destination);
            if (finalDistance <= WAYPOINT_REACHED_DISTANCE) {
                logger.info("Successfully reached destination");
                return true;
            } else {
                logger.warning("Did not reach destination. Final distance: " + String.format("%.1f", finalDistance));
                return false;
            }

        } finally {
            walkLock.unlock();
        }
    }

    /**
     * Walks to a single waypoint using minimap clicking.
     * 
     * @param waypoint The waypoint to walk to
     * @param timeout Maximum time to wait
     * @return true if reached waypoint, false otherwise
     */
    private static boolean walkToWaypoint(Tile waypoint, long timeout) throws InterruptedException {
        Tile startLoc = Player.getLocation();
        double startDistance = startLoc.distanceTo(waypoint);

        // If already close enough, consider it reached
        if (startDistance <= WAYPOINT_REACHED_DISTANCE) {
            return true;
        }

        // Convert to local coordinates for minimap clicking
        int localX = waypoint.getX() - rt4.Camera.originX;
        int localY = waypoint.getY() - rt4.Camera.originZ;

        // Click on minimap
        logger.info("Clicking minimap at local coordinates (" + localX + ", " + localY + ")");
        
        // Set the map flag (this is what the game uses to show the red X)
        rt4.LoginManager.mapFlagX = localX;
        rt4.LoginManager.mapFlagZ = localY;

        // Send the walk packet
        rt4.PacketSender.sendWalkPacket(localX, localY, false);

        // Wait for player to start moving
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < 2000) { // 2 second timeout for movement to start
            if (Thread.currentThread().isInterrupted()) {
                throw new InterruptedException("Walking interrupted");
            }

            if (Player.isMoving()) {
                break;
            }
            Thread.sleep(50);
        }

        // Wait for player to reach waypoint or stop moving
        startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeout) {
            if (Thread.currentThread().isInterrupted()) {
                throw new InterruptedException("Walking interrupted");
            }

            Tile currentLoc = Player.getLocation();
            double currentDistance = currentLoc.distanceTo(waypoint);

            // Check if we've reached the waypoint
            if (currentDistance <= WAYPOINT_REACHED_DISTANCE) {
                logger.info("Reached waypoint");
                return true;
            }

            // Check if player stopped moving (might be stuck)
            if (!Player.isMoving()) {
                logger.warning("Player stopped moving before reaching waypoint. Distance remaining: " + 
                             String.format("%.1f", currentDistance));
                return currentDistance <= WAYPOINT_REACHED_DISTANCE * 2; // Allow some tolerance
            }

            Thread.sleep(100);
        }

        // Timeout reached
        Tile finalLoc = Player.getLocation();
        double finalDistance = finalLoc.distanceTo(waypoint);
        logger.warning("Timeout walking to waypoint. Final distance: " + String.format("%.1f", finalDistance));
        return finalDistance <= WAYPOINT_REACHED_DISTANCE;
    }

    /**
     * Convenience method - walks to destination with default timeout
     */
    public static boolean walkTo(Tile destination) throws InterruptedException, TimeoutException {
        return walkTo(destination, DEFAULT_WALK_TIMEOUT);
    }

    /**
     * Walks along a predefined path of waypoints
     */
    public static boolean walkPath(Tile[] path, long timeout) throws InterruptedException, TimeoutException {
        if (path == null || path.length == 0) {
            throw new IllegalArgumentException("Path cannot be null or empty");
        }

        logger.info("Walking predefined path with " + path.length + " waypoints");

        for (int i = 0; i < path.length; i++) {
            if (Thread.currentThread().isInterrupted()) {
                throw new InterruptedException("Path walking interrupted");
            }

            Tile waypoint = path[i];
            logger.info("Walking to path waypoint " + (i + 1) + "/" + path.length + ": " + waypoint);

            if (!walkToWaypoint(waypoint, DEFAULT_WAYPOINT_TIMEOUT)) {
                logger.warning("Failed to reach path waypoint " + waypoint);
                return false;
            }
        }

        logger.info("Successfully completed path");
        return true;
    }

    /**
     * Convenience method - walks path with default timeout
     */
    public static boolean walkPath(Tile[] path) throws InterruptedException, TimeoutException {
        return walkPath(path, DEFAULT_WALK_TIMEOUT);
    }

    /**
     * Checks if a tile is reachable (has a valid path)
     */
    public static boolean isReachable(Tile destination) {
        if (destination == null) {
            return false;
        }

        Tile playerLoc = Player.getLocation();
        List<Tile> path = PathfindingUtils.findPath(playerLoc, destination);
        return path != null && !path.isEmpty();
    }

    /**
     * Gets the distance to a destination
     */
    public static double getDistanceTo(Tile destination) {
        if (destination == null) {
            return Double.MAX_VALUE;
        }
        return Player.getLocation().distanceTo(destination);
    }
}

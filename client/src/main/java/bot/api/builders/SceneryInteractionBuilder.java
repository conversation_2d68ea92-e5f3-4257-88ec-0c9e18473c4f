package bot.api.builders;

import bot.core.ScriptManager;
import bot.api.interaction.SceneryInteraction;
import bot.api.interaction.WalkInteraction;
import bot.api.interaction.WidgetInteraction;
import bot.util.entity.EntityUtils;
import bot.util.error.TimeoutConfig;
import bot.util.game.world.Tile;
import rt4.PlayerList;
import rt4.Scenery;

import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;

import java.util.concurrent.TimeoutException;

/**
 * A clean, focused builder for scenery interactions in the task chaining framework.
 * Provides a chainable API for common scenery interaction tasks.
 */
public class SceneryInteractionBuilder extends AbstractActionBuilder<SceneryInteractionBuilder> {

    private static final Logger logger = LoggerFactory.getLogger(SceneryInteractionBuilder.class.getSimpleName());

    // Scenery configuration
    private int sceneryId = -1;
    private int[] sceneryIds = null;
    private String interactionAction = null;

    // Animation configuration
    private boolean waitForAnimation = false;
    private boolean waitForAnyAnimation = false;
    private int expectedAnimationId = -1;

    // Interface configuration
    private boolean waitForInterface = false;
    private int waitInterfaceId = -1;

    /**
     * Creates a new SceneryInteractionBuilder without targeting a specific scenery.
     */
    public SceneryInteractionBuilder() {
        // Default constructor
    }

    /**
     * Creates a new SceneryInteractionBuilder targeting a specific scenery ID.
     *
     * @param sceneryId the ID of the scenery to interact with
     */
    public SceneryInteractionBuilder(int sceneryId) {
        this.sceneryId = sceneryId;
        this.sceneryIds = null;
    }

    /**
     * Creates a new SceneryInteractionBuilder targeting multiple scenery IDs.
     * The builder will target the nearest scenery with any of these IDs.
     *
     * @param sceneryIds array of scenery IDs to interact with
     */
    public SceneryInteractionBuilder(int[] sceneryIds) {
        this.sceneryIds = sceneryIds;
        this.sceneryId = -1;
    }

    /**
     * Sets the scenery ID to interact with.
     *
     * @param sceneryId the ID of the scenery to interact with
     * @return this builder for method chaining
     */
    public SceneryInteractionBuilder with(int sceneryId) {
        this.sceneryId = sceneryId;
        this.sceneryIds = null; // Reset array when setting single ID
        return this;
    }

    /**
     * Sets multiple scenery IDs to interact with.
     * The builder will target the nearest scenery with any of these IDs.
     *
     * @param sceneryIds array of scenery IDs to interact with
     * @return this builder for method chaining
     */
    public SceneryInteractionBuilder with(int[] sceneryIds) {
        this.sceneryIds = sceneryIds;
        this.sceneryId = -1; // Reset single ID when setting array
        return this;
    }

    /**
     * Sets the action to use when interacting with the scenery.
     *
     * @param action the action to use (e.g., "Mine", "Chop down", "Open")
     * @return this builder for method chaining
     */
    public SceneryInteractionBuilder action(String action) {
        this.interactionAction = action;
        return this;
    }

    // Using standardized method from AbstractActionBuilder for walk distance

    /**
     * Configures the builder to wait for a specific animation after interacting.
     *
     * @param animationId the expected animation ID
     * @return this builder for method chaining
     */
    public SceneryInteractionBuilder waitForAnimation(int animationId) {
        this.waitForAnimation = true;
        this.waitForAnyAnimation = false;
        this.expectedAnimationId = animationId;
        return this;
    }

    /**
     * Configures the builder to wait for any animation change after interacting.
     * This is more flexible than waiting for a specific animation ID.
     *
     * @return this builder for method chaining
     */
    public SceneryInteractionBuilder waitForAnyAnimation() {
        this.waitForAnimation = true;
        this.waitForAnyAnimation = true;
        this.expectedAnimationId = -1;
        return this;
    }

    /**
     * Sets the timeout for waiting for an animation to start.
     *
     * @param milliseconds the timeout in milliseconds
     * @return this builder for method chaining
     */
    public SceneryInteractionBuilder withAnimationTimeout(int milliseconds) {
        this.timeoutConfig = new TimeoutConfig(this.timeoutConfig)
                .withAnimationTimeout(milliseconds);
        return this;
    }

    /**
     * Configures the builder to wait for a specific interface to open after interacting.
     *
     * @param interfaceId The interface ID to wait for
     * @param timeout The maximum time to wait in milliseconds
     * @return this builder for method chaining
     */
    public SceneryInteractionBuilder waitForInterface(int interfaceId, int timeout) {
        this.waitForInterface = true;
        this.waitInterfaceId = interfaceId;
        this.timeoutConfig = new TimeoutConfig(this.timeoutConfig)
                .withInterfaceTimeout(timeout);
        return this;
    }

    @Override
    protected boolean executeAction() throws InterruptedException, TimeoutException {
        // Validate configuration
        if (sceneryId == -1 && (sceneryIds == null || sceneryIds.length == 0)) {
            logger.warning("No scenery ID specified");
            return false;
        }

        if (interactionAction == null || interactionAction.isEmpty()) {
            logger.warning("No interaction action specified");
            return false;
        }

        // Find the nearest scenery object
        Scenery scenery;
        if (sceneryIds != null && sceneryIds.length > 0) {
            scenery = EntityUtils.findNearestScenery(sceneryIds);
        } else {
            scenery = EntityUtils.findNearestScenery(sceneryId);
        }
        if (scenery == null) {
            if (sceneryIds != null && sceneryIds.length > 0) {
                logger.warning("Could not find scenery with any of the specified IDs");
            } else {
                logger.warning("Could not find scenery with ID: " + sceneryId);
            }
            return false;
        }

        // Check if the scenery is on screen
        boolean onScreen = bot.util.entity.ScreenUtils.isSceneryOnScreen(scenery);

        if (onScreen) {
            // Scenery is on screen, interact with it
            logger.info("Scenery is on screen, interacting with action: " + interactionAction);

            try {
                boolean interactionSuccess = SceneryInteraction.interactWithScenery(scenery, interactionAction, timeoutConfig.getInterfaceTimeout());
                if (!interactionSuccess) {
                    logger.warning("Failed to interact with scenery");
                    return false;
                }
            } catch (TimeoutException e) {
                logger.warning("Timeout while interacting with scenery: " + e.getMessage());
                throw e; // Propagate the timeout exception to be handled by AbstractActionBuilder
            }

            // Add natural delay after interaction
            sleep(minDelay, maxDelay);

            // Wait for animation if configured
            if (waitForAnimation) {
                boolean animationResult = waitForExpectedAnimation();
                if (!animationResult) {
                    return false;
                }
            }

            // Wait for interface if configured
            if (waitForInterface) {
                return waitForExpectedInterface();
            }

            return true;
        } else {
            // Scenery is not on screen, check distance
            Tile sceneryTile = EntityUtils.getSceneryTile(scenery);

            // Check if the scenery is within the maximum walk distance
            if (!isWithinWalkDistance(sceneryTile)) {
                return false;
            }

            // Walk to the scenery
            logger.info("Walking to scenery at " + sceneryTile);
            WalkInteraction.walkToLocal(sceneryTile);

            // Wait for arrival and try again
            Thread.sleep(1000);
            return executeAction();
        }
    }

    /**
     * Waits for the expected animation to start.
     *
     * @return true if the animation started within the timeout, false otherwise
     */
    private boolean waitForExpectedAnimation() throws InterruptedException {
        if (expectedAnimationId == -1) {
            logger.warning("No expected animation ID specified");
            return true; // Continue without waiting for animation
        }

        logger.info("Waiting for animation ID: " + expectedAnimationId);

        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutConfig.getAnimationTimeout()) {
            // Check if script is paused
            if (ScriptManager.getInstance().isScriptPaused()) {
                logger.info("Script paused while waiting for animation");
                // Wait until the script is resumed or interrupted
                while (ScriptManager.getInstance().isScriptPaused() && !Thread.currentThread().isInterrupted()) {
                    Thread.sleep(50);
                }
                // If the thread was interrupted while paused, throw an exception
                if (Thread.currentThread().isInterrupted()) {
                    logger.info("Thread interrupted while paused waiting for animation");
                    throw new InterruptedException("Thread interrupted while paused waiting for animation");
                }
                // Reset the start time to account for the pause
                long pauseDuration = System.currentTimeMillis() - startTime;
                startTime = System.currentTimeMillis() - pauseDuration;
            }

            // Check if thread has been interrupted
            if (Thread.currentThread().isInterrupted()) {
                logger.info("Thread interrupted while waiting for animation");
                throw new InterruptedException("Thread interrupted while waiting for animation");
            }

            int currentAnimation = PlayerList.self.seqId;

            if (currentAnimation == expectedAnimationId) {
                logger.info("Expected animation started");
                return true;
            }

            Thread.sleep(100);
        }

        logger.warning("Timed out waiting for animation");
        return false;
    }

    /**
     * Waits for the expected interface to open.
     *
     * @return true if the interface opened within the timeout, false otherwise
     */
    private boolean waitForExpectedInterface() throws InterruptedException {
        if (waitInterfaceId == -1) {
            logger.warning("No expected interface ID specified");
            return true; // Continue without waiting for interface
        }

        logger.info("Waiting for interface ID: " + waitInterfaceId);

        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutConfig.getInterfaceTimeout()) {
            // Check if script is paused
            if (ScriptManager.getInstance().isScriptPaused()) {
                logger.info("Script paused while waiting for interface");
                // Wait until the script is resumed or interrupted
                while (ScriptManager.getInstance().isScriptPaused() && !Thread.currentThread().isInterrupted()) {
                    Thread.sleep(50);
                }
                // If the thread was interrupted while paused, throw an exception
                if (Thread.currentThread().isInterrupted()) {
                    logger.info("Thread interrupted while paused waiting for interface");
                    throw new InterruptedException("Thread interrupted while paused waiting for interface");
                }
                // Reset the start time to account for the pause
                long pauseDuration = System.currentTimeMillis() - startTime;
                startTime = System.currentTimeMillis() - pauseDuration;
            }

            // Check if thread has been interrupted
            if (Thread.currentThread().isInterrupted()) {
                logger.info("Thread interrupted while waiting for interface");
                throw new InterruptedException("Thread interrupted while waiting for interface");
            }

            if (WidgetInteraction.isInterfaceOpen(waitInterfaceId)) {
                logger.info("Expected interface opened");
                return true;
            }

            Thread.sleep(100);
        }

        logger.warning("Timed out waiting for interface ID: " + waitInterfaceId);
        return false;
    }
}
